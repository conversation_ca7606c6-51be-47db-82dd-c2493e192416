import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowLeft,
  FaStore,
  FaSave,
  FaSync,
  FaMoneyBillWave,
  FaReceipt,
  FaCog,
  FaCheck,
  FaSun,
  FaMoon,
  FaDesktop,
  FaKey,
  FaShieldAlt,
  FaDownload,
  FaUpload,
  FaExclamationTriangle,
  FaUndo,
  FaDatabase,
  FaCloud,
  FaVolumeUp,
  FaInfoCircle
} from 'react-icons/fa';

import api from '../lib/axios';
import { useAuthStore } from '../stores/authStore';
import { useTheme } from '../contexts/ThemeContext';
import { TextInput, TextArea, NumberInput } from '../components/inputs';
import ToggleSwitch from '../components/ToggleSwitch';

import SuccessModal from '../components/SuccessModal';
import SimpleFolderPicker from '../components/SimpleFolderPicker';
import BackupPathTestModal from '../components/BackupPathTestModal';
import ScheduledTasksManager from '../components/ScheduledTasksManager';
import GoogleDriveManager from '../components/GoogleDriveManager';
import DeviceSecuritySettings from '../components/DeviceSecuritySettings';
import NumberFormatSettings from '../components/NumberFormatSettings';
import ChatAudioSettings from '../components/ChatAudioSettings';
import DateTimeSettings from '../components/DateTimeSettings';

interface SettingGroup {
  id: string;
  name: string;
  icon: React.ReactNode;
  settings: Setting[];
}

interface Setting {
  key: string;
  name: string;
  value: string;
  type: 'text' | 'number' | 'boolean' | 'textarea' | 'theme' | 'folder';
  description?: string;
}

const Settings: React.FC = () => {
  const [activeGroup, setActiveGroup] = useState('store');
  const [settings, setSettings] = useState<Record<string, string>>({});
  const [originalSettings, setOriginalSettings] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [errorMessage, setErrorMessage] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [showBackupTestModal, setShowBackupTestModal] = useState(false);
  const [backupTestResult, setBackupTestResult] = useState<any>(null);

  const { user } = useAuthStore();
  const { setTheme } = useTheme();
  const navigate = useNavigate();

  // Define setting groups
  const settingGroups: SettingGroup[] = [
    {
      id: 'store',
      name: 'معلومات المتجر',
      icon: <FaStore />,
      settings: [
        { key: 'store_name', name: 'اسم المتجر', value: '', type: 'text' },
        { key: 'store_address', name: 'عنوان المتجر', value: '', type: 'textarea' },
        { key: 'store_phone', name: 'رقم الهاتف', value: '', type: 'text' },
        { key: 'store_email', name: 'البريد الإلكتروني', value: '', type: 'text' }
      ]
    },
    {
      id: 'currency',
      name: 'العملة والضرائب',
      icon: <FaMoneyBillWave />,
      settings: [
        { key: 'tax_rate', name: 'نسبة الضريبة (%)', value: '', type: 'number' },
        { key: 'tax_included', name: 'الأسعار تشمل الضريبة', value: '', type: 'boolean' }
      ]
    },
    {
      id: 'receipt',
      name: 'إعدادات الفاتورة',
      icon: <FaReceipt />,
      settings: [
        { key: 'receipt_header', name: 'رأس الفاتورة', value: '', type: 'textarea' },
        { key: 'receipt_footer', name: 'تذييل الفاتورة', value: '', type: 'textarea' },
        { key: 'receipt_show_tax', name: 'إظهار الضريبة في الفاتورة', value: '', type: 'boolean' },
        { key: 'receipt_show_cashier', name: 'إظهار اسم الكاشير في الفاتورة', value: '', type: 'boolean' }
      ]
    },
    {
      id: 'system',
      name: 'إعدادات النظام',
      icon: <FaCog />,
      settings: [
        { key: 'low_stock_threshold', name: 'حد المخزون المنخفض', value: '', type: 'number' },
        { key: 'auto_refresh_data', name: 'تحديث البيانات تلقائياً', value: '', type: 'boolean' },
        { key: 'show_cashier_profits', name: 'إظهار أرباح اليوم للكاشير', value: '', type: 'boolean', description: 'السماح للمستخدمين العاديين (الكاشير) برؤية أرباح اليوم في لوحة التحكم' },
        { key: 'theme_mode', name: 'مظهر التطبيق', value: '', type: 'theme', description: 'اختر بين المظهر الفاتح والمظهر الداكن' },
        { key: 'system_activated', name: 'تفعيل النظام', value: '', type: 'boolean', description: 'تفعيل أو إلغاء تفعيل النظام' }
      ]
    },
    {
      id: 'backup',
      name: 'النسخ الاحتياطية',
      icon: <FaDatabase />,
      settings: [
        { key: 'backup_path', name: 'مسار حفظ النسخ الاحتياطية', value: '', type: 'folder', description: 'حدد المجلد الذي تريد حفظ النسخ الاحتياطية فيه' }
      ]
    },
    {
      id: 'google_drive',
      name: 'Google Drive',
      icon: <FaCloud />,
      settings: [
        { key: 'google_drive_enabled', name: 'تفعيل Google Drive', value: '', type: 'boolean', description: 'تفعيل النسخ الاحتياطي التلقائي إلى Google Drive' },
        { key: 'google_drive_auto_cleanup', name: 'تنظيف تلقائي', value: '', type: 'boolean', description: 'حذف النسخ الاحتياطية القديمة تلقائياً من Google Drive' },
        { key: 'google_drive_keep_count', name: 'عدد النسخ للاحتفاظ بها', value: '', type: 'number', description: 'عدد النسخ الاحتياطية للاحتفاظ بها في Google Drive' },
        { key: 'google_drive_delete_local_after_upload', name: 'حذف النسخة المحلية بعد الرفع', value: '', type: 'boolean', description: 'حذف النسخة المحلية بعد رفعها إلى Google Drive بنجاح' }
      ]
    },
    {
      id: 'device_security',
      name: 'أمان الأجهزة',
      icon: <FaShieldAlt />,
      settings: []
    },
    {
      id: 'chat_audio',
      name: 'المحادثة والصوت',
      icon: <FaVolumeUp />,
      settings: []
    },
    {
      id: 'license',
      name: 'الترخيص والتفعيل',
      icon: <FaKey />,
      settings: [
        { key: 'license_key', name: 'مفتاح الترخيص', value: '', type: 'text', description: 'أدخل مفتاح الترخيص الخاص بك لتفعيل النظام' },
        { key: 'license_status', name: 'حالة الترخيص', value: '', type: 'text', description: 'حالة الترخيص الحالية' },
        { key: 'license_expires', name: 'تاريخ انتهاء الترخيص', value: '', type: 'text', description: 'تاريخ انتهاء صلاحية الترخيص' }
      ]
    }
  ];

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/api/settings');
      const settingsData = response.data.reduce((acc: Record<string, string>, curr: any) => {
        acc[curr.key] = curr.value;
        return acc;
      }, {});

      // Add theme setting from localStorage if not in API response
      if (!settingsData.theme_mode) {
        settingsData.theme_mode = localStorage.getItem('theme') || 'light';
      }

      setSettings(settingsData);
      setOriginalSettings(settingsData);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching settings:', error);
      setErrorMessage('فشل في تحميل الإعدادات. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Check if settings have changed
  const hasChanges = (): boolean => {
    return Object.keys(settings).some(key => settings[key] !== originalSettings[key]);
  };

  // Check if there are validation errors
  const hasValidationErrors = (): boolean => {
    return Object.keys(settings).some(key => validateSetting(key, settings[key]) !== null);
  };

  // Handle input change
  const handleChange = (key: string, value: string | boolean) => {
    // If changing theme, apply it immediately
    if (key === 'theme_mode') {
      setTheme(value as 'light' | 'dark' | 'system');
    }

    // If changing system activation, show confirmation
    if (key === 'system_activated' && value !== settings[key]) {
      const isActivating = value === true || value === 'true';
      const message = isActivating
        ? 'هل أنت متأكد من تفعيل النظام؟'
        : 'هل أنت متأكد من إلغاء تفعيل النظام؟ سيؤثر هذا على جميع العمليات.';

      if (!window.confirm(message)) {
        return;
      }
    }

    setSettings({
      ...settings,
      [key]: value.toString()
    });
  };

  // Handle save settings
  const saveSettings = async () => {
    setIsSaving(true);
    try {
      // Validate all settings before saving
      const validationErrors: string[] = [];
      Object.keys(settings).forEach(key => {
        const error = validateSetting(key, settings[key]);
        if (error) {
          validationErrors.push(`${key}: ${error}`);
        }
      });

      if (validationErrors.length > 0) {
        setErrorMessage('يرجى تصحيح الأخطاء التالية: ' + validationErrors.join(', '));
        setIsSaving(false);
        setTimeout(() => setErrorMessage(''), 5000);
        return;
      }

      const settingsToUpdate = Object.keys(settings)
        .filter(key => settings[key] !== originalSettings[key])
        .map(key => ({
          key,
          value: settings[key]
        }));

      if (settingsToUpdate.length > 0) {
        await api.post('/api/settings/batch', { settings: settingsToUpdate });

        setOriginalSettings(settings);
        setModalMessage('تم حفظ الإعدادات بنجاح');
        setShowSuccessModal(true);
      } else {
        setModalMessage('لا توجد تغييرات للحفظ');
        setShowSuccessModal(true);
      }

      setIsSaving(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrorMessage('فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
      setIsSaving(false);

      // Clear error message after 3 seconds
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Reset settings to original values
  const resetSettings = () => {
    setSettings(originalSettings);
  };

  // Export settings to JSON file
  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `smartpos-settings-${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    setModalMessage('تم تصدير الإعدادات بنجاح');
    setShowSuccessModal(true);
  };

  // Test backup path
  const testBackupPath = async () => {
    try {
      const response = await api.post('/api/dashboard/test-backup-path');
      setBackupTestResult(response.data);
      setShowBackupTestModal(true);
    } catch (error: any) {
      console.error('Error testing backup path:', error);
      const errorResult = {
        success: false,
        backup_path: settings.backup_path || 'غير محدد',
        absolute_path: 'غير متاح',
        exists: false,
        is_writable: false,
        message: error.response?.data?.detail || 'فشل في اختبار مسار النسخ الاحتياطية'
      };
      setBackupTestResult(errorResult);
      setShowBackupTestModal(true);
    }
  };

  // Import settings from JSON file
  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);

        // Validate imported settings
        if (typeof importedSettings !== 'object' || importedSettings === null) {
          throw new Error('Invalid settings format');
        }

        // Merge with current settings
        const newSettings = { ...settings, ...importedSettings };
        setSettings(newSettings);

        setModalMessage('تم استيراد الإعدادات بنجاح');
        setShowSuccessModal(true);
      } catch (error) {
        setErrorMessage('فشل في استيراد الإعدادات. تأكد من صحة الملف.');
        setTimeout(() => setErrorMessage(''), 3000);
      }
    };

    reader.readAsText(file);
    // Reset file input
    event.target.value = '';
  };

  // Activate license
  const activateLicense = async (licenseKey: string) => {
    try {
      // Simulate license validation (in real app, this would call an API)
      if (licenseKey.length < 10) {
        throw new Error('مفتاح الترخيص غير صحيح');
      }

      // Update license status
      const newSettings = {
        ...settings,
        license_status: 'نشط',
        license_expires: '2025-12-31',
        system_activated: 'true'
      };

      setSettings(newSettings);
      setModalMessage('تم تفعيل الترخيص بنجاح');
      setShowSuccessModal(true);
    } catch (error) {
      setErrorMessage('فشل في تفعيل الترخيص. تأكد من صحة المفتاح.');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // Reset to default settings
  const resetToDefaults = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
      const defaultSettings = {
        store_name: 'متجر ذكي',
        store_address: 'طرابلس، ليبيا',
        store_phone: '+218-91-1234567',
        store_email: '<EMAIL>',
        currency_symbol: 'د.ل',
        decimal_places: '2',
        tax_rate: '0',
        tax_included: 'false',
        receipt_header: 'مرحباً بكم في متجرنا',
        receipt_footer: 'شكراً لزيارتكم',
        receipt_show_tax: 'true',
        receipt_show_cashier: 'true',
        low_stock_threshold: '10',
        auto_refresh_data: 'true',
        show_cashier_profits: 'true',
        theme_mode: 'light',
        system_activated: 'true',
        backup_path: 'backups',
        license_key: '',
        license_status: 'نشط',
        license_expires: '2025-12-31',
        chat_sound_enabled: 'true',
        chat_sound_volume: '70',
        chat_sound_id: 'notification'
      };

      setSettings(defaultSettings);
      setModalMessage('تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
      setShowSuccessModal(true);
    }
  };

  // Validate setting value
  const validateSetting = (key: string, value: string): string | null => {
    switch (key) {
      case 'store_email':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return 'البريد الإلكتروني غير صحيح';
        }
        break;
      case 'store_phone':
        if (value && !/^[\d\s\-\+\(\)]+$/.test(value)) {
          return 'رقم الهاتف غير صحيح';
        }
        break;
      case 'decimal_places':
        if (value && (parseInt(value) < 0 || parseInt(value) > 4)) {
          return 'عدد الأرقام العشرية يجب أن يكون بين 0 و 4';
        }
        break;
      case 'tax_rate':
        if (value && (parseFloat(value) < 0 || parseFloat(value) > 100)) {
          return 'نسبة الضريبة يجب أن تكون بين 0 و 100';
        }
        break;
      case 'low_stock_threshold':
        if (value && parseInt(value) < 0) {
          return 'حد المخزون المنخفض يجب أن يكون أكبر من أو يساوي 0';
        }
        break;
    }
    return null;
  };

  // Render input field based on setting type
  const renderInputField = (setting: Setting) => {
    const value = settings[setting.key] || '';
    const validationError = validateSetting(setting.key, value);

    switch (setting.type) {
      case 'textarea':
        return (
          <TextArea
            name={setting.key}
            value={value}
            onChange={(newValue) => handleChange(setting.key, newValue)}
            placeholder={`أدخل ${setting.name}...`}
            rows={3}
            error={validationError || undefined}
          />
        );

      case 'number':
        return (
          <NumberInput
            name={setting.key}
            value={value}
            onChange={(newValue) => handleChange(setting.key, newValue)}
            placeholder={`أدخل ${setting.name}...`}
            min={setting.key === 'decimal_places' ? 0 : undefined}
            max={setting.key === 'decimal_places' ? 4 : setting.key === 'tax_rate' ? 100 : undefined}
            step={setting.key === 'tax_rate' ? '0.01' : '1'}
            error={validationError || undefined}
          />
        );

      case 'boolean':
        return (
          <div className="bg-white dark:bg-gray-700 p-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-12 flex items-center">
            <ToggleSwitch
              id={setting.key}
              checked={value === 'true'}
              onChange={(checked) => handleChange(setting.key, checked)}
              label={value === 'true' ? 'مفعل' : 'غير مفعل'}
              className="w-full"
            />
          </div>
        );

      case 'theme':
        return (
          <div className="flex flex-col space-y-3">
            <div className="flex space-x-4 space-x-reverse">
              <button
                type="button"
                onClick={() => handleChange(setting.key, 'light')}
                className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${
                  value === 'light'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/30'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <FaSun className="text-2xl text-yellow-500 mb-2" />
                <span className="text-sm font-medium dark:text-gray-200">فاتح</span>
              </button>

              <button
                type="button"
                onClick={() => handleChange(setting.key, 'dark')}
                className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${
                  value === 'dark'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/30'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <FaMoon className="text-2xl text-gray-700 dark:text-gray-400 mb-2" />
                <span className="text-sm font-medium dark:text-gray-200">داكن</span>
              </button>

              <button
                type="button"
                onClick={() => handleChange(setting.key, 'system')}
                className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${
                  value === 'system'
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/30'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <FaDesktop className="text-2xl text-blue-500 mb-2" />
                <span className="text-sm font-medium dark:text-gray-200">تلقائي</span>
              </button>
            </div>
          </div>
        );

      case 'folder':
        return (
          <SimpleFolderPicker
            value={value}
            onChange={(newValue: string) => handleChange(setting.key, newValue)}
            onTest={testBackupPath}
            placeholder="أدخل مسار المجلد للنسخ الاحتياطية..."
          />
        );

      case 'text':
      default:
        return (
          <div>
            <TextInput
              name={setting.key}
              type={setting.key === 'license_key' ? 'password' : setting.key === 'store_email' ? 'email' : 'text'}
              value={value}
              onChange={(newValue) => handleChange(setting.key, newValue)}
              placeholder={
                setting.key === 'license_key' ? 'أدخل مفتاح الترخيص...' :
                setting.key === 'store_email' ? '<EMAIL>' :
                `أدخل ${setting.name}...`
              }
              disabled={setting.key === 'license_status' || setting.key === 'license_expires'}
              error={validationError || undefined}
              icon={setting.key === 'license_key' ? <FaKey /> : undefined}
            />
            {setting.key === 'license_key' && value && (
              <button
                onClick={() => activateLicense(value)}
                className="mt-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-green-600 hover:border-green-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-green-500/20 shadow-lg hover:shadow-xl gap-2"
              >
                <FaShieldAlt className="ml-2" />
                تفعيل الترخيص
              </button>
            )}
            {setting.key === 'license_status' && value === 'نشط' && (
              <p className="text-green-600 dark:text-green-400 text-sm mt-1 flex items-center">
                <FaCheck className="ml-2" />
                الترخيص نشط ومفعل
              </p>
            )}
            {setting.key === 'license_status' && value !== 'نشط' && value && (
              <p className="text-red-600 dark:text-red-400 text-sm mt-1 flex items-center">
                <FaExclamationTriangle className="ml-2" />
                الترخيص غير نشط
              </p>
            )}
          </div>
        );
    }
  };

  // Check if user is admin
  if (user?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 p-4 rounded-lg">
          يجب أن تكون مديراً للوصول إلى صفحة الإعدادات.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={() => navigate('/')}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FaCog className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">إعدادات النظام</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  تخصيص وإدارة إعدادات التطبيق
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap">
              <button
                onClick={exportSettings}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="تصدير الإعدادات"
              >
                <FaDownload className="text-sm" />
              </button>

              <label className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 cursor-pointer rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600" title="استيراد الإعدادات">
                <FaUpload className="text-sm" />
                <input
                  type="file"
                  accept=".json"
                  onChange={importSettings}
                  className="hidden"
                />
              </label>

              <button
                onClick={resetToDefaults}
                className="text-gray-600 dark:text-gray-300 hover:text-orange-600 dark:hover:text-orange-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="إعادة تعيين إلى القيم الافتراضية"
              >
                <FaUndo className="text-sm" />
              </button>

              <button
                onClick={fetchSettings}
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600"
                title="تحديث"
                disabled={isLoading}
              >
                <FaSync className={`text-sm ${isLoading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={saveSettings}
                disabled={!hasChanges() || isSaving || hasValidationErrors()}
                className={`bg-primary-600 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl ${
                  !hasChanges() || isSaving || hasValidationErrors() ? 'opacity-50 cursor-not-allowed' : 'hover:bg-primary-700 hover:border-primary-700'
                }`}
              >
                <FaSave className="ml-2 text-sm" />
                <span className="hidden sm:inline lg:inline">حفظ التغييرات</span>
                <span className="sm:hidden lg:hidden">حفظ</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}

      {errorMessage && (
        <div className="bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-red-200 dark:border-red-800">
          <FaExclamationTriangle className="ml-3 text-lg flex-shrink-0" />
          <span className="font-medium">{errorMessage}</span>
        </div>
      )}

      {/* System Status Warning */}
      {settings.system_activated === 'false' && (
        <div className="bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-yellow-200 dark:border-yellow-800">
          <FaExclamationTriangle className="ml-3 text-lg flex-shrink-0" />
          <div>
            <span className="font-medium">تحذير:</span> النظام غير مفعل حالياً. قم بتفعيله من إعدادات النظام.
          </div>
        </div>
      )}

      {/* License Expiry Warning */}
      {settings.license_expires && new Date(settings.license_expires) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) && (
        <div className="bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-orange-200 dark:border-orange-800">
          <FaExclamationTriangle className="ml-3 text-lg flex-shrink-0" />
          <div>
            <span className="font-medium">تنبيه:</span> سينتهي الترخيص قريباً في {settings.license_expires}. يرجى تجديد الترخيص.
          </div>
        </div>
      )}

      {/* Settings Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 sticky top-6">
            <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
              <h3 className="font-semibold text-gray-800 dark:text-gray-200 text-center">أقسام الإعدادات</h3>
            </div>
            <div className="p-2">
                {settingGroups.map((group) => {
                  // تحديد الألوان لكل قسم
                  const getGroupColors = (groupId: string) => {
                    const colorMap: Record<string, { bg: string; icon: string; border: string }> = {
                      'store': {
                        bg: 'bg-blue-100 dark:bg-blue-900/30',
                        icon: 'text-blue-600 dark:text-blue-300',
                        border: 'border-blue-200 dark:border-blue-700'
                      },
                      'currency': {
                        bg: 'bg-green-100 dark:bg-green-900/30',
                        icon: 'text-green-600 dark:text-green-300',
                        border: 'border-green-200 dark:border-green-700'
                      },
                      'receipt': {
                        bg: 'bg-purple-100 dark:bg-purple-900/30',
                        icon: 'text-purple-600 dark:text-purple-300',
                        border: 'border-purple-200 dark:border-purple-700'
                      },
                      'system': {
                        bg: 'bg-orange-100 dark:bg-orange-900/30',
                        icon: 'text-orange-600 dark:text-orange-300',
                        border: 'border-orange-200 dark:border-orange-700'
                      },
                      'backup': {
                        bg: 'bg-indigo-100 dark:bg-indigo-900/30',
                        icon: 'text-indigo-600 dark:text-indigo-300',
                        border: 'border-indigo-200 dark:border-indigo-700'
                      },
                      'google_drive': {
                        bg: 'bg-red-100 dark:bg-red-900/30',
                        icon: 'text-red-600 dark:text-red-300',
                        border: 'border-red-200 dark:border-red-700'
                      },
                      'device_security': {
                        bg: 'bg-yellow-100 dark:bg-yellow-900/30',
                        icon: 'text-yellow-600 dark:text-yellow-300',
                        border: 'border-yellow-200 dark:border-yellow-700'
                      }
                    };
                    return colorMap[groupId] || {
                      bg: 'bg-gray-100 dark:bg-gray-900/30',
                      icon: 'text-gray-600 dark:text-gray-300',
                      border: 'border-gray-200 dark:border-gray-700'
                    };
                  };

                  const colors = getGroupColors(group.id);

                  return (
                    <button
                      key={group.id}
                      onClick={() => setActiveGroup(group.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 text-right ${
                        activeGroup === group.id
                          ? 'bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      <div className={`p-2 rounded-lg ${colors.bg}`}>
                        <span className={`${colors.icon}`}>
                          {group.icon}
                        </span>
                      </div>
                      <span className="font-medium text-sm">{group.name}</span>
                    </button>
                  );
                })}
            </div>
          </div>
        </div>

        {/* Settings Form */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            {isLoading ? (
              <div className="flex flex-col justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">جارٍ تحميل الإعدادات...</p>
              </div>
            ) : (
              <>
                <div className="p-6 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/30 dark:to-blue-900/30 border-b border-gray-200 dark:border-gray-600">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                    <div className="p-2 rounded-lg bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 ml-3">
                      {settingGroups.find(group => group.id === activeGroup)?.icon}
                    </div>
                    {settingGroups.find(group => group.id === activeGroup)?.name}
                  </h2>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {/* عرض الإعدادات العادية لجميع الأقسام عدا النظام */}
                    {activeGroup !== 'system' && settingGroups
                      .find(group => group.id === activeGroup)
                      ?.settings.map((setting, index) => (
                        <div key={setting.key} className={`${
                          index !== (settingGroups.find(group => group.id === activeGroup)?.settings.length || 0) - 1
                            ? 'border-b border-gray-100 dark:border-gray-700 pb-6'
                            : ''
                        }`}>
                          <label className="block mb-3">
                            <span className="text-gray-700 dark:text-gray-200 font-medium text-lg">{setting.name}</span>
                            {setting.description && (
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 leading-relaxed">{setting.description}</p>
                            )}
                          </label>
                          {renderInputField(setting)}
                        </div>
                      ))}

                    {/* إضافة مكون إدارة المهام المجدولة في قسم النسخ الاحتياطية */}
                    {activeGroup === 'backup' && (
                      <div className="border-t border-gray-100 dark:border-gray-700 pt-6">
                        <ScheduledTasksManager backupPath={settings.backup_path || 'backups'} />
                      </div>
                    )}

                    {/* إضافة مكون إدارة Google Drive في قسم Google Drive */}
                    {activeGroup === 'google_drive' && (
                      <div className="border-t border-gray-100 dark:border-gray-700 pt-6">
                        <GoogleDriveManager />
                      </div>
                    )}

                    {/* إضافة مكون تنسيق الأرقام المالية في قسم العملة والضرائب */}
                    {activeGroup === 'currency' && (
                      <div className="border-t border-gray-100 dark:border-gray-700 pt-6">
                        <NumberFormatSettings
                          settings={settings}
                          onSettingChange={(key: string, value: string) => {
                            setSettings({
                              ...settings,
                              [key]: value
                            });
                          }}
                        />
                      </div>
                    )}

                    {/* إضافة مكون إعدادات أمان الأجهزة */}
                    {activeGroup === 'device_security' && (
                      <div>
                        <DeviceSecuritySettings />
                      </div>
                    )}

                    {/* تبويب إعدادات النظام المحسن */}
                    {activeGroup === 'system' && (
                      <div className="space-y-6">
                        {/* عرض الإعدادات الأساسية للنظام بنفس التنسيق */}
                        {settingGroups
                          .find(group => group.id === 'system')
                          ?.settings.map((setting, index) => (
                            <div key={setting.key} className={`${
                              index !== (settingGroups.find(group => group.id === 'system')?.settings.length || 0) - 1
                                ? 'border-b border-gray-100 dark:border-gray-700 pb-6'
                                : ''
                            }`}>
                              <label className="block mb-3">
                                <span className="text-gray-700 dark:text-gray-200 font-medium text-lg">{setting.name}</span>
                                {setting.description && (
                                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 leading-relaxed">{setting.description}</p>
                                )}
                              </label>
                              {renderInputField(setting)}
                            </div>
                          ))}

                        {/* قسم إعدادات التاريخ والوقت */}
                        <div className="border-t border-gray-100 dark:border-gray-700 pt-6">
                          <DateTimeSettings
                            settings={settings}
                            onSettingChange={(key: string, value: string) => {
                              setSettings({
                                ...settings,
                                [key]: value
                              });
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {/* إضافة مكون إعدادات المحادثة والصوت */}
                    {activeGroup === 'chat_audio' && (
                      <div className="border-t border-gray-100 dark:border-gray-700 pt-6">
                        <ChatAudioSettings
                          settings={settings}
                          onSettingChange={(key: string, value: string) => {
                            setSettings({
                              ...settings,
                              [key]: value
                            });
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {hasChanges() && (
                  <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                      <div className="text-sm">
                        {hasValidationErrors() ? (
                          <div className="text-red-600 dark:text-red-400">
                            <span className="font-medium">يوجد أخطاء في البيانات</span> - يرجى تصحيحها قبل الحفظ
                          </div>
                        ) : (
                          <div className="text-gray-600 dark:text-gray-400">
                            <span className="font-medium">تم تعديل الإعدادات</span> - لا تنس حفظ التغييرات
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <button
                          onClick={resetSettings}
                          className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px]"
                        >
                          إلغاء التغييرات
                        </button>
                        <button
                          onClick={saveSettings}
                          disabled={isSaving || hasValidationErrors()}
                          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <FaSave className="ml-2" />
                          <span>{isSaving ? 'جارٍ الحفظ...' : 'حفظ التغييرات'}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="نجح العملية"
        message={modalMessage}
      />

      {/* Backup Path Test Modal */}
      <BackupPathTestModal
        isOpen={showBackupTestModal}
        onClose={() => setShowBackupTestModal(false)}
        testResult={backupTestResult}
      />
    </div>
  );
};

export default Settings;