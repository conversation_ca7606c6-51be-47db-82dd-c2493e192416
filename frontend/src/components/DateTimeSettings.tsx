/**
 * مكون إعدادات التاريخ والوقت والمنطقة الزمنية
 * يتيح للمستخدم التحكم في تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';
import { FaClock, FaGlobe, FaCalendarAlt, FaCheck, FaSpinner } from 'react-icons/fa';
import { SelectInput, TextInput } from './inputs';
import { formatDateTime, getCurrentTripoliDateTime } from '../services/dateTimeService';

interface DateTimeSettingsProps {
  settings: Record<string, string>;
  onSettingChange: (key: string, value: string) => void;
}

interface DateTimeFormatOption {
  value: string;
  label: string;
  example: string;
}

interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

const DateTimeSettings: React.FC<DateTimeSettingsProps> = ({
  settings,
  onSettingChange
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [previewDate, setPreviewDate] = useState<Date>(new Date());

  // خيارات تنسيق التاريخ
  const dateFormatOptions: DateTimeFormatOption[] = [
    { value: 'dd/MM/yyyy', label: 'يوم/شهر/سنة', example: '15/07/2025' },
    { value: 'MM/dd/yyyy', label: 'شهر/يوم/سنة', example: '07/15/2025' },
    { value: 'yyyy-MM-dd', label: 'سنة-شهر-يوم', example: '2025-07-15' },
    { value: 'dd-MM-yyyy', label: 'يوم-شهر-سنة', example: '15-07-2025' },
    { value: 'dd.MM.yyyy', label: 'يوم.شهر.سنة', example: '15.07.2025' },
    { value: 'arabic', label: 'التاريخ العربي', example: '15 يوليو 2025' }
  ];

  // خيارات تنسيق الوقت
  const timeFormatOptions: DateTimeFormatOption[] = [
    { value: '24h', label: '24 ساعة', example: '14:30' },
    { value: '12h', label: '12 ساعة', example: '2:30 PM' },
    { value: '12h_ar', label: '12 ساعة عربي', example: '2:30 م' }
  ];

  // خيارات المناطق الزمنية
  const timezoneOptions: TimezoneOption[] = [
    { value: 'Africa/Tripoli', label: 'طرابلس، ليبيا', offset: 'UTC+2' },
    { value: 'Africa/Cairo', label: 'القاهرة، مصر', offset: 'UTC+2' },
    { value: 'Asia/Riyadh', label: 'الرياض، السعودية', offset: 'UTC+3' },
    { value: 'Asia/Dubai', label: 'دبي، الإمارات', offset: 'UTC+4' },
    { value: 'Europe/London', label: 'لندن، بريطانيا', offset: 'UTC+0' },
    { value: 'America/New_York', label: 'نيويورك، أمريكا', offset: 'UTC-5' }
  ];

  // تحديث معاينة التاريخ كل ثانية
  useEffect(() => {
    const interval = setInterval(() => {
      setPreviewDate(getCurrentTripoliDateTime());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // الحصول على القيم الحالية من الإعدادات
  const currentDateFormat = settings.date_format || 'dd/MM/yyyy';
  const currentTimeFormat = settings.time_format || '24h';
  const currentTimezone = settings.timezone || 'Africa/Tripoli';
  const currentLanguage = settings.date_language || 'ar';

  // تنسيق التاريخ للمعاينة
  const formatPreviewDate = (date: Date): string => {
    try {
      const format = currentDateFormat === 'arabic' ? 'datetime' : 'date';
      return formatDateTime(date, format as any);
    } catch (error) {
      console.error('Error formatting preview date:', error);
      return date.toLocaleDateString('ar-LY');
    }
  };

  // تنسيق الوقت للمعاينة
  const formatPreviewTime = (date: Date): string => {
    try {
      switch (currentTimeFormat) {
        case '12h':
          return date.toLocaleTimeString('en-US', { 
            hour12: true, 
            hour: 'numeric', 
            minute: '2-digit' 
          });
        case '12h_ar':
          return date.toLocaleTimeString('ar-LY', { 
            hour12: true, 
            hour: 'numeric', 
            minute: '2-digit' 
          });
        default:
          return date.toLocaleTimeString('en-GB', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
          });
      }
    } catch (error) {
      console.error('Error formatting preview time:', error);
      return date.toLocaleTimeString();
    }
  };

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex items-center space-x-3 space-x-reverse mb-6">
        <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
          <FaClock className="text-primary-600 dark:text-primary-400 text-xl" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إعدادات التاريخ والوقت
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            تخصيص تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
          </p>
        </div>
      </div>

      {/* معاينة مباشرة */}
      <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 p-4 rounded-xl border border-primary-200 dark:border-primary-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <FaCalendarAlt className="text-primary-600 dark:text-primary-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              المعاينة المباشرة:
            </span>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {formatPreviewDate(previewDate)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {formatPreviewTime(previewDate)}
            </div>
          </div>
        </div>
      </div>

      {/* إعدادات التنسيق */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* تنسيق التاريخ */}
        <div className="space-y-3">
          <SelectInput
            name="date_format"
            label="تنسيق التاريخ"
            value={currentDateFormat}
            onChange={(value) => onSettingChange('date_format', value)}
            options={dateFormatOptions.map(option => ({
              value: option.value,
              label: `${option.label} (${option.example})`,
              icon: <FaCalendarAlt className="text-primary-500" />
            }))}
            icon={<FaCalendarAlt />}
            className="w-full"
          />
        </div>

        {/* تنسيق الوقت */}
        <div className="space-y-3">
          <SelectInput
            name="time_format"
            label="تنسيق الوقت"
            value={currentTimeFormat}
            onChange={(value) => onSettingChange('time_format', value)}
            options={timeFormatOptions.map(option => ({
              value: option.value,
              label: `${option.label} (${option.example})`,
              icon: <FaClock className="text-primary-500" />
            }))}
            icon={<FaClock />}
            className="w-full"
          />
        </div>
      </div>

      {/* المنطقة الزمنية */}
      <div className="space-y-3">
        <SelectInput
          name="timezone"
          label="المنطقة الزمنية"
          value={currentTimezone}
          onChange={(value) => onSettingChange('timezone', value)}
          options={timezoneOptions.map(option => ({
            value: option.value,
            label: `${option.label} (${option.offset})`,
            icon: <FaGlobe className="text-primary-500" />
          }))}
          icon={<FaGlobe />}
          className="w-full"
          searchable={true}
        />
      </div>

      {/* لغة التاريخ */}
      <div className="space-y-3">
        <SelectInput
          name="date_language"
          label="لغة عرض التاريخ"
          value={currentLanguage}
          onChange={(value) => onSettingChange('date_language', value)}
          options={[
            { value: 'ar', label: 'العربية', icon: <span className="text-primary-500">ع</span> },
            { value: 'en', label: 'English', icon: <span className="text-primary-500">E</span> }
          ]}
          className="w-full"
        />
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-700">
        <div className="flex items-start space-x-3 space-x-reverse">
          <FaCheck className="text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0" />
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="font-medium mb-1">ملاحظات مهمة:</p>
            <ul className="space-y-1 text-xs">
              <li>• سيتم تطبيق التغييرات على جميع أجزاء التطبيق</li>
              <li>• المنطقة الزمنية الافتراضية هي طرابلس، ليبيا (UTC+2)</li>
              <li>• يمكن معاينة التغييرات قبل الحفظ</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateTimeSettings;
