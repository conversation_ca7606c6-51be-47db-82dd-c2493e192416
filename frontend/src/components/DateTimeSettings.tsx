/**
 * مكون إعدادات التاريخ والوقت والمنطقة الزمنية
 * يتيح للمستخدم التحكم في تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
 * يطبق مبادئ البرمجة الكائنية والتصميم الموحد للنظام
 */

import React, { useState, useEffect } from 'react';
import { FaClock, FaGlobe, FaCalendarAlt, FaCheck, FaSpinner, FaSync, FaMapMarkerAlt, FaWifi } from 'react-icons/fa';
import { SelectInput } from './inputs';
import { formatDateTime, getCurrentTripoliDateTime } from '../services/dateTimeService';
import { timezoneDetectionService, TimezoneInfo } from '../services/timezoneDetectionService';

interface DateTimeSettingsProps {
  settings: Record<string, string>;
  onSettingChange: (key: string, value: string) => void;
}

interface DateTimeFormatOption {
  value: string;
  label: string;
  example: string;
}

interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

const DateTimeSettings: React.FC<DateTimeSettingsProps> = ({
  settings,
  onSettingChange
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [previewDate, setPreviewDate] = useState<Date>(new Date());
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectedTimezone, setDetectedTimezone] = useState<TimezoneInfo | null>(null);
  const [showDetectionResult, setShowDetectionResult] = useState(false);

  // خيارات تنسيق التاريخ
  const dateFormatOptions: DateTimeFormatOption[] = [
    { value: 'dd/MM/yyyy', label: 'يوم/شهر/سنة', example: '15/07/2025' },
    { value: 'MM/dd/yyyy', label: 'شهر/يوم/سنة', example: '07/15/2025' },
    { value: 'yyyy-MM-dd', label: 'سنة-شهر-يوم', example: '2025-07-15' },
    { value: 'dd-MM-yyyy', label: 'يوم-شهر-سنة', example: '15-07-2025' },
    { value: 'dd.MM.yyyy', label: 'يوم.شهر.سنة', example: '15.07.2025' },
    { value: 'arabic', label: 'التاريخ العربي', example: '15 يوليو 2025' }
  ];

  // خيارات تنسيق الوقت
  const timeFormatOptions: DateTimeFormatOption[] = [
    { value: '24h', label: '24 ساعة', example: '14:30' },
    { value: '12h', label: '12 ساعة', example: '2:30 PM' },
    { value: '12h_ar', label: '12 ساعة عربي', example: '2:30 م' }
  ];

  // خيارات المناطق الزمنية (مرتبة حسب الاستخدام في التطبيقات الشهيرة)
  const timezoneOptions: TimezoneOption[] = [
    // المناطق العربية والشرق الأوسط
    { value: 'Africa/Tripoli', label: 'طرابلس، ليبيا', offset: 'UTC+2' },
    { value: 'Africa/Cairo', label: 'القاهرة، مصر', offset: 'UTC+2' },
    { value: 'Asia/Riyadh', label: 'الرياض، السعودية', offset: 'UTC+3' },
    { value: 'Asia/Dubai', label: 'دبي، الإمارات', offset: 'UTC+4' },
    { value: 'Asia/Kuwait', label: 'الكويت', offset: 'UTC+3' },
    { value: 'Asia/Qatar', label: 'الدوحة، قطر', offset: 'UTC+3' },
    { value: 'Asia/Bahrain', label: 'المنامة، البحرين', offset: 'UTC+3' },
    { value: 'Asia/Baghdad', label: 'بغداد، العراق', offset: 'UTC+3' },
    { value: 'Asia/Damascus', label: 'دمشق، سوريا', offset: 'UTC+3' },
    { value: 'Asia/Amman', label: 'عمان، الأردن', offset: 'UTC+3' },
    { value: 'Asia/Beirut', label: 'بيروت، لبنان', offset: 'UTC+3' },
    { value: 'Africa/Tunis', label: 'تونس', offset: 'UTC+1' },
    { value: 'Africa/Algiers', label: 'الجزائر', offset: 'UTC+1' },
    { value: 'Africa/Casablanca', label: 'الدار البيضاء، المغرب', offset: 'UTC+1' },

    // أمريكا الشمالية (Google, Facebook, Microsoft, Apple)
    { value: 'America/New_York', label: 'نيويورك، أمريكا الشرقية', offset: 'UTC-5' },
    { value: 'America/Los_Angeles', label: 'لوس أنجلوس، أمريكا الغربية', offset: 'UTC-8' },
    { value: 'America/Chicago', label: 'شيكاغو، أمريكا الوسطى', offset: 'UTC-6' },
    { value: 'America/Denver', label: 'دنفر، أمريكا الجبلية', offset: 'UTC-7' },
    { value: 'America/Toronto', label: 'تورونتو، كندا', offset: 'UTC-5' },
    { value: 'America/Vancouver', label: 'فانكوفر، كندا', offset: 'UTC-8' },

    // أوروبا (SAP, Spotify, Skype)
    { value: 'Europe/London', label: 'لندن، بريطانيا', offset: 'UTC+0' },
    { value: 'Europe/Paris', label: 'باريس، فرنسا', offset: 'UTC+1' },
    { value: 'Europe/Berlin', label: 'برلين، ألمانيا', offset: 'UTC+1' },
    { value: 'Europe/Rome', label: 'روما، إيطاليا', offset: 'UTC+1' },
    { value: 'Europe/Madrid', label: 'مدريد، إسبانيا', offset: 'UTC+1' },
    { value: 'Europe/Amsterdam', label: 'أمستردام، هولندا', offset: 'UTC+1' },
    { value: 'Europe/Stockholm', label: 'ستوكهولم، السويد', offset: 'UTC+1' },
    { value: 'Europe/Zurich', label: 'زيورخ، سويسرا', offset: 'UTC+1' },
    { value: 'Europe/Moscow', label: 'موسكو، روسيا', offset: 'UTC+3' },

    // آسيا والمحيط الهادئ (Alibaba, Tencent, Sony, Samsung)
    { value: 'Asia/Tokyo', label: 'طوكيو، اليابان', offset: 'UTC+9' },
    { value: 'Asia/Seoul', label: 'سيول، كوريا الجنوبية', offset: 'UTC+9' },
    { value: 'Asia/Shanghai', label: 'شنغهاي، الصين', offset: 'UTC+8' },
    { value: 'Asia/Hong_Kong', label: 'هونغ كونغ', offset: 'UTC+8' },
    { value: 'Asia/Singapore', label: 'سنغافورة', offset: 'UTC+8' },
    { value: 'Asia/Kolkata', label: 'كولكاتا، الهند', offset: 'UTC+5:30' },
    { value: 'Asia/Mumbai', label: 'مومباي، الهند', offset: 'UTC+5:30' },
    { value: 'Asia/Bangkok', label: 'بانكوك، تايلاند', offset: 'UTC+7' },
    { value: 'Asia/Jakarta', label: 'جاكرتا، إندونيسيا', offset: 'UTC+7' },
    { value: 'Asia/Manila', label: 'مانيلا، الفلبين', offset: 'UTC+8' },
    { value: 'Australia/Sydney', label: 'سيدني، أستراليا', offset: 'UTC+10' },
    { value: 'Australia/Melbourne', label: 'ملبورن، أستراليا', offset: 'UTC+10' },
    { value: 'Pacific/Auckland', label: 'أوكلاند، نيوزيلندا', offset: 'UTC+12' },

    // أمريكا الجنوبية (MercadoLibre, Globo)
    { value: 'America/Sao_Paulo', label: 'ساو باولو، البرازيل', offset: 'UTC-3' },
    { value: 'America/Buenos_Aires', label: 'بوينس آيرس، الأرجنتين', offset: 'UTC-3' },
    { value: 'America/Santiago', label: 'سانتياغو، تشيلي', offset: 'UTC-4' },
    { value: 'America/Lima', label: 'ليما، بيرو', offset: 'UTC-5' },
    { value: 'America/Bogota', label: 'بوغوتا، كولومبيا', offset: 'UTC-5' },
    { value: 'America/Mexico_City', label: 'مكسيكو سيتي، المكسيك', offset: 'UTC-6' },

    // أفريقيا
    { value: 'Africa/Johannesburg', label: 'جوهانسبرغ، جنوب أفريقيا', offset: 'UTC+2' },
    { value: 'Africa/Lagos', label: 'لاغوس، نيجيريا', offset: 'UTC+1' },
    { value: 'Africa/Nairobi', label: 'نيروبي، كينيا', offset: 'UTC+3' },

    // مناطق خاصة
    { value: 'UTC', label: 'التوقيت العالمي المنسق (UTC)', offset: 'UTC+0' },
    { value: 'GMT', label: 'توقيت غرينتش (GMT)', offset: 'UTC+0' }
  ];

  // تحديث معاينة التاريخ كل ثانية
  useEffect(() => {
    const interval = setInterval(() => {
      setPreviewDate(getCurrentTripoliDateTime());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // وظيفة الكشف التلقائي عن المنطقة الزمنية
  const handleAutoDetectTimezone = async () => {
    setIsDetecting(true);
    setShowDetectionResult(false);

    try {
      const detected = await timezoneDetectionService.comprehensiveTimezoneDetection();
      setDetectedTimezone(detected);
      setShowDetectionResult(true);

      // تطبيق المنطقة الزمنية المكتشفة تلقائياً إذا كان الإعداد مفعلاً
      if (settings.auto_detect_timezone === 'true') {
        onSettingChange('timezone', detected.timezone);
      }
    } catch (error) {
      console.error('Error detecting timezone:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  // وظيفة تطبيق المنطقة الزمنية المكتشفة
  const applyDetectedTimezone = () => {
    if (detectedTimezone) {
      onSettingChange('timezone', detectedTimezone.timezone);
      setShowDetectionResult(false);
    }
  };

  // الحصول على القيم الحالية من الإعدادات
  const currentDateFormat = settings.date_format || 'dd/MM/yyyy';
  const currentTimeFormat = settings.time_format || '24h';
  const currentTimezone = settings.timezone || 'Africa/Tripoli';
  const currentLanguage = settings.date_language || 'ar';

  // تنسيق التاريخ للمعاينة
  const formatPreviewDate = (date: Date): string => {
    try {
      const format = currentDateFormat === 'arabic' ? 'datetime' : 'date';
      return formatDateTime(date, format as any);
    } catch (error) {
      console.error('Error formatting preview date:', error);
      return date.toLocaleDateString('ar-LY');
    }
  };

  // تنسيق الوقت للمعاينة
  const formatPreviewTime = (date: Date): string => {
    try {
      switch (currentTimeFormat) {
        case '12h':
          return date.toLocaleTimeString('en-US', { 
            hour12: true, 
            hour: 'numeric', 
            minute: '2-digit' 
          });
        case '12h_ar':
          return date.toLocaleTimeString('ar-LY', { 
            hour12: true, 
            hour: 'numeric', 
            minute: '2-digit' 
          });
        default:
          return date.toLocaleTimeString('en-GB', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
          });
      }
    } catch (error) {
      console.error('Error formatting preview time:', error);
      return date.toLocaleTimeString();
    }
  };

  return (
    <div className="space-y-6">
      {/* عنوان القسم */}
      <div className="flex items-center space-x-3 space-x-reverse mb-6">
        <div className="p-3 bg-primary-100 dark:bg-primary-900/30 rounded-xl">
          <FaClock className="text-primary-600 dark:text-primary-400 text-xl" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إعدادات التاريخ والوقت
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            تخصيص تنسيق التاريخ والوقت والمنطقة الزمنية للتطبيق
          </p>
        </div>
      </div>

      {/* معاينة مباشرة */}
      <div className="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 p-4 rounded-xl border border-primary-200 dark:border-primary-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <FaCalendarAlt className="text-primary-600 dark:text-primary-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              المعاينة المباشرة:
            </span>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {formatPreviewDate(previewDate)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {formatPreviewTime(previewDate)}
            </div>
          </div>
        </div>
      </div>

      {/* إعدادات التنسيق */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* تنسيق التاريخ */}
        <div className="space-y-3">
          <SelectInput
            name="date_format"
            label="تنسيق التاريخ"
            value={currentDateFormat}
            onChange={(value) => onSettingChange('date_format', value)}
            options={dateFormatOptions.map(option => ({
              value: option.value,
              label: `${option.label} (${option.example})`,
              icon: <FaCalendarAlt className="text-primary-500" />
            }))}
            icon={<FaCalendarAlt />}
            className="w-full"
          />
        </div>

        {/* تنسيق الوقت */}
        <div className="space-y-3">
          <SelectInput
            name="time_format"
            label="تنسيق الوقت"
            value={currentTimeFormat}
            onChange={(value) => onSettingChange('time_format', value)}
            options={timeFormatOptions.map(option => ({
              value: option.value,
              label: `${option.label} (${option.example})`,
              icon: <FaClock className="text-primary-500" />
            }))}
            icon={<FaClock />}
            className="w-full"
          />
        </div>
      </div>

      {/* المنطقة الزمنية */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-gray-700 dark:text-gray-200 font-medium text-lg">
            المنطقة الزمنية
          </label>
          <button
            type="button"
            onClick={handleAutoDetectTimezone}
            disabled={isDetecting}
            className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDetecting ? (
              <FaSpinner className="animate-spin" />
            ) : (
              <FaSync />
            )}
            <span>{isDetecting ? 'جارٍ الكشف...' : 'كشف تلقائي'}</span>
          </button>
        </div>

        <SelectInput
          name="timezone"
          value={currentTimezone}
          onChange={(value) => onSettingChange('timezone', value)}
          options={timezoneOptions.map(option => ({
            value: option.value,
            label: `${option.label} (${option.offset})`,
            icon: <FaGlobe className="text-primary-500" />
          }))}
          icon={<FaGlobe />}
          className="w-full"
          searchable={true}
        />

        {/* نتيجة الكشف التلقائي */}
        {showDetectionResult && detectedTimezone && (
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-xl border border-green-200 dark:border-green-700">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 space-x-reverse">
                <FaMapMarkerAlt className="text-green-600 dark:text-green-400 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-1">
                    تم اكتشاف منطقتك الزمنية
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    <strong>{detectedTimezone.city}, {detectedTimezone.country}</strong>
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    {detectedTimezone.timezone} ({detectedTimezone.offsetString})
                    {detectedTimezone.isDST && ' - التوقيت الصيفي نشط'}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  type="button"
                  onClick={applyDetectedTimezone}
                  className="px-3 py-1 text-xs bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  تطبيق
                </button>
                <button
                  type="button"
                  onClick={() => setShowDetectionResult(false)}
                  className="px-3 py-1 text-xs bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* لغة التاريخ */}
      <div className="space-y-3">
        <SelectInput
          name="date_language"
          label="لغة عرض التاريخ"
          value={currentLanguage}
          onChange={(value) => onSettingChange('date_language', value)}
          options={[
            { value: 'ar', label: 'العربية', icon: <span className="text-primary-500">ع</span> },
            { value: 'en', label: 'English', icon: <span className="text-primary-500">E</span> }
          ]}
          className="w-full"
        />
      </div>

      {/* إعدادات التحديث التلقائي */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <FaWifi className="text-blue-500 mr-2" />
          إعدادات التحديث التلقائي
        </h4>

        <div className="space-y-4">
          {/* الكشف التلقائي عن المنطقة الزمنية */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                الكشف التلقائي عن المنطقة الزمنية
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                كشف المنطقة الزمنية تلقائياً عند تحميل الصفحة
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.auto_detect_timezone === 'true'}
                onChange={(e) => onSettingChange('auto_detect_timezone', e.target.checked.toString())}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>

          {/* تحديث الوقت من الإنترنت */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                تحديث الوقت من الإنترنت
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                مزامنة الوقت مع خوادم الإنترنت للحصول على دقة أعلى
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.sync_internet_time === 'true'}
                onChange={(e) => onSettingChange('sync_internet_time', e.target.checked.toString())}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-700">
        <div className="flex items-start space-x-3 space-x-reverse">
          <FaCheck className="text-blue-600 dark:text-blue-400 mt-1 flex-shrink-0" />
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="font-medium mb-1">ملاحظات مهمة:</p>
            <ul className="space-y-1 text-xs">
              <li>• سيتم تطبيق التغييرات على جميع أجزاء التطبيق</li>
              <li>• المنطقة الزمنية الافتراضية هي طرابلس، ليبيا (UTC+2)</li>
              <li>• يمكن معاينة التغييرات قبل الحفظ</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateTimeSettings;
